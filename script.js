// 示例数据
const exampleData = {
    "userInput": {
        "D_text": "我真是气死了！今年7月15号，我在他们那个官方网站上花了一万两千多（具体是12888块）买了台叫"幻影X Pro"的笔记本，订单号是一串数字880123456789。18号电脑就到了，本来还挺开心的。结果好家伙，用了还不到一个月，从8月10号开始吧，这电脑就老是无缘无故蓝屏，然后卡死不动，机身烫得跟个暖手宝似的，我的好多工作文件都因为这个搞丢了！我找他们客服理论，客服就一直踢皮球，翻来覆去就说是我自己装了什么不兼容的软件，还让我上传日志、拍视频，折腾了好半天，最后就说只给修，不给退不给换，修还要一个月！这不明摆着是电脑本身就有毛病吗？我坚决不同意维修，我就要退货退款！这事儿太耽误我工作了！",
        "C_claim": "我就一个要求，把我买电脑的12888块钱全退给我！还有，因为这破电脑我好几天班都没上好，他们至少得赔我2000块钱的误工费！"
    },
    "systemOutput": {
        "L_document": {
            "title": "民事起诉状",
            "plaintiff": {
                "label": "原告",
                "name": "张三",
                "gender": "男",
                "birthDate": "1995年10月20日",
                "ethnicity": "汉族",
                "idNumber": "11010119951020XXXX",
                "address": "北京市朝阳区建国路XX号院X号楼X单元XXX室",
                "phone": "138-0013-8000"
            },
            "defendant": {
                "label": "被告",
                "name": "未来科技电脑有限公司",
                "creditCode": "91440300MA5GXXXXXX",
                "address": "广东省深圳市南山区科技园路XX号",
                "legalRepresentative": "李四",
                "representativeTitle": "法定代表人",
                "phone": "0755-88886666"
            },
            "caseCause": "买卖合同纠纷",
            "claims": {
                "title": "诉讼请求",
                "items": [
                    "一、判令被告立即退还原告购物款人民币12,888元；",
                    "二、判令被告赔偿原告因处理本事件导致的误工损失人民币2,000元；",
                    "三、判令被告承担本案全部诉讼费用。"
                ]
            },
            "factsAndReasons": {
                "title": "事实与理由",
                "sections": [
                    {
                        "heading": "一、消费事实基本情况",
                        "content": "原告于2025年7月15日，通过被告运营的官方网站（www.futuretech.com）购买了"幻影X Pro"型笔记本电脑一台，并通过在线支付方式支付货款共计人民币12,888元，相关订单号为880123456789。该商品由被告安排物流并于同年7月18日送达原告处，双方之间的买卖合同关系依法成立且生效。"
                    }
                ]
            }
        },
        "R_analysis": {
            "reportSummary": {
                "caseStrengthScore": 78,
                "keyEvidenceGaps": 1,
                "strategySuggestions": 3
            },
            "evidenceAnalysis": {
                "gapDiagnosis": [
                    "关键证据缺失：当前证据主要依赖原告单方陈述和记录，虽能构成初步证据链，但被告可能抗辩称故障系人为原因导致。一份由第三方权威机构出具的质检报告，将是锁定被告责任、反驳其抗辩理由的核心证据，建议尽快获取。"
                ]
            },
            "legalInsights": {
                "relevantStatutes": [
                    {
                        "statuteName": "《中华人民共和国消费者权益保护法》第二十四条",
                        "content": "经营者提供的商品或者服务不符合质量要求的，消费者可以依照国家规定、当事人约定退货，或者要求经营者履行更换、修理等义务..."
                    }
                ],
                "guidingCases": [
                    {
                        "caseTitle": "王某诉某电子品牌商产品责任纠纷案",
                        "caseNumber": "（2024）沪01民终XXXX号",
                        "judgmentSummary": "法院认为，消费者购买的产品在"三包"期内出现非人为性能故障，经两次修理仍不能正常使用的，经营者应当负责更换或者退货。"
                    }
                ]
            },
            "courtSimulation": {
                "adversarialSummary": {
                    "ourArguments": [
                        "产品在三包期内出现严重性能故障，符合法定退货条件。",
                        "被告以不实理由（如软件不兼容）推卸责任，属于恶意违约。",
                        "产品质量问题直接导致了原告的误工损失，应予赔偿。"
                    ],
                    "opponentDefenses": [
                        "故障可能是由原告自行安装的第三方软件或病毒导致，非产品本身质量问题。",
                        "原告未能提供权威的检测报告证明产品存在固有缺陷。",
                        "原告主张的误工损失缺乏充分的证据支持。"
                    ]
                },
                "strategyOptimization": [
                    "首要策略：立即将设备送至具有司法鉴定资质的第三方检测机构进行检测，获取产品存在质量缺陷的直接证据。",
                    "次要策略：整理并公证与客服的完整沟通记录，固定被告消极应对和推诿责任的证据。",
                    "补充策略：准备误工损失的相关证据，如劳动合同、请假记录或因设备问题导致项目延误的沟通邮件等。"
                ]
            }
        }
    }
};

// 平滑滚动到指定区域
function scrollToSection(sectionId) {
    const element = document.getElementById(sectionId);
    if (element) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// 填充示例案例
function fillExampleCase() {
    const caseDescription = document.getElementById('case-description');
    const legalClaim = document.getElementById('legal-claim');
    
    caseDescription.value = exampleData.userInput.D_text;
    legalClaim.value = exampleData.userInput.C_claim;
    
    // 添加填充动画效果
    caseDescription.style.background = '#f0f9ff';
    legalClaim.style.background = '#f0f9ff';
    
    setTimeout(() => {
        caseDescription.style.background = '';
        legalClaim.style.background = '';
    }, 1000);
    
    // 更新步骤指示器
    updateStepIndicator(2);
}

// 更新步骤指示器
function updateStepIndicator(activeStep) {
    const steps = document.querySelectorAll('.step');
    steps.forEach((step, index) => {
        if (index + 1 <= activeStep) {
            step.classList.add('active');
        } else {
            step.classList.remove('active');
        }
    });
}

// 开始分析
function startAnalysis() {
    const caseDescription = document.getElementById('case-description').value.trim();
    const legalClaim = document.getElementById('legal-claim').value.trim();
    
    if (!caseDescription || !legalClaim) {
        alert('请填写完整的案情描述和诉讼诉求');
        return;
    }
    
    const analyzeBtn = document.querySelector('.analyze-btn');
    const btnText = analyzeBtn.querySelector('.btn-text');
    const loadingSpinner = analyzeBtn.querySelector('.loading-spinner');
    
    // 显示加载状态
    btnText.style.display = 'none';
    loadingSpinner.style.display = 'flex';
    analyzeBtn.disabled = true;
    
    // 模拟分析过程
    setTimeout(() => {
        // 隐藏加载状态
        btnText.style.display = 'inline';
        loadingSpinner.style.display = 'none';
        analyzeBtn.disabled = false;
        
        // 显示结果区域
        const resultsSection = document.getElementById('results');
        resultsSection.style.display = 'block';
        
        // 生成文档和分析报告
        generateDocument();
        generateAnalysisReport();
        
        // 滚动到结果区域
        scrollToSection('results');
    }, 3000);
}

// 生成文档内容
function generateDocument() {
    const documentContent = document.getElementById('document-content');
    const doc = exampleData.systemOutput.L_document;
    
    let html = `
        <div class="document-section">
            <h4 style="text-align: center; margin-bottom: 2rem; font-size: 1.5rem;">${doc.title}</h4>
            
            <div class="party-info">
                <p><strong>${doc.plaintiff.label}：</strong>${doc.plaintiff.name}，${doc.plaintiff.gender}，${doc.plaintiff.birthDate}生，${doc.plaintiff.ethnicity}族，身份证号：${doc.plaintiff.idNumber}，住址：${doc.plaintiff.address}，联系电话：${doc.plaintiff.phone}。</p>
                
                <p><strong>${doc.defendant.label}：</strong>${doc.defendant.name}，统一社会信用代码：${doc.defendant.creditCode}，住所地：${doc.defendant.address}，${doc.defendant.representativeTitle}：${doc.defendant.legalRepresentative}，联系电话：${doc.defendant.phone}。</p>
            </div>
            
            <div class="case-cause">
                <p><strong>案由：</strong>${doc.caseCause}</p>
            </div>
            
            <div class="claims-section">
                <h5>${doc.claims.title}：</h5>
                <ol>
                    ${doc.claims.items.map(item => `<li>${item}</li>`).join('')}
                </ol>
            </div>
            
            <div class="facts-section">
                <h5>${doc.factsAndReasons.title}：</h5>
                ${doc.factsAndReasons.sections.map(section => `
                    <div class="fact-section">
                        <h6>${section.heading}</h6>
                        <p>${section.content}</p>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
    
    documentContent.innerHTML = html;
}

// 生成分析报告
function generateAnalysisReport() {
    const analysis = exampleData.systemOutput.R_analysis;
    
    // 更新概览卡片
    document.getElementById('case-strength').textContent = analysis.reportSummary.caseStrengthScore + '%';
    document.getElementById('evidence-gaps').textContent = analysis.reportSummary.keyEvidenceGaps;
    document.getElementById('strategy-suggestions').textContent = analysis.reportSummary.strategySuggestions;
    
    // 生成证据链分析
    generateEvidenceChain();
    
    // 生成法律洞察
    generateLegalInsights();
    
    // 生成法庭推演
    generateCourtSimulation();
}

// 生成证据链可视化
function generateEvidenceChain() {
    const evidenceChain = document.getElementById('evidence-chain');
    
    const html = `
        <div class="evidence-timeline">
            <div class="timeline-item evidence-owned">
                <div class="timeline-marker"></div>
                <div class="timeline-content">
                    <h6>网络购物订单</h6>
                    <p>证明买卖关系成立</p>
                </div>
            </div>
            <div class="timeline-item evidence-owned">
                <div class="timeline-marker"></div>
                <div class="timeline-content">
                    <h6>在线支付凭证</h6>
                    <p>证明交易金额和时间</p>
                </div>
            </div>
            <div class="timeline-item evidence-owned">
                <div class="timeline-marker"></div>
                <div class="timeline-content">
                    <h6>故障现象录像</h6>
                    <p>初步证明产品质量问题</p>
                </div>
            </div>
            <div class="timeline-item evidence-gap">
                <div class="timeline-marker"></div>
                <div class="timeline-content">
                    <h6>第三方质检报告</h6>
                    <p class="gap-warning">⚠️ 关键证据缺失</p>
                </div>
            </div>
        </div>
    `;
    
    evidenceChain.innerHTML = html;
    
    // 生成证据缺口诊断
    const gapDiagnosis = document.getElementById('gap-diagnosis');
    gapDiagnosis.innerHTML = `
        <div class="gap-alert">
            <div class="alert-icon">⚠️</div>
            <div class="alert-content">
                <h6>证据缺口诊断</h6>
                <p>${exampleData.systemOutput.R_analysis.evidenceAnalysis.gapDiagnosis[0]}</p>
            </div>
        </div>
    `;
}

// 生成法律洞察
function generateLegalInsights() {
    const legalInsights = document.getElementById('legal-insights');
    const insights = exampleData.systemOutput.R_analysis.legalInsights;
    
    let html = `
        <div class="legal-statutes">
            <h6>相关法律条款</h6>
            ${insights.relevantStatutes.map(statute => `
                <div class="statute-card">
                    <h7>${statute.statuteName}</h7>
                    <p>${statute.content}</p>
                </div>
            `).join('')}
        </div>
        
        <div class="guiding-cases">
            <h6>指导案例</h6>
            ${insights.guidingCases.map(case_ => `
                <div class="case-card">
                    <h7>${case_.caseTitle}</h7>
                    <p class="case-number">${case_.caseNumber}</p>
                    <p>${case_.judgmentSummary}</p>
                </div>
            `).join('')}
        </div>
    `;
    
    legalInsights.innerHTML = html;
}

// 生成法庭推演
function generateCourtSimulation() {
    const courtSimulation = document.getElementById('court-simulation');
    const simulation = exampleData.systemOutput.R_analysis.courtSimulation;
    
    let html = `
        <div class="adversarial-arguments">
            <div class="arguments-grid">
                <div class="our-arguments">
                    <h6><i class="fas fa-thumbs-up"></i> 我方主张</h6>
                    <ul>
                        ${simulation.adversarialSummary.ourArguments.map(arg => `<li>${arg}</li>`).join('')}
                    </ul>
                </div>
                <div class="opponent-defenses">
                    <h6><i class="fas fa-shield-alt"></i> 对方可能抗辩</h6>
                    <ul>
                        ${simulation.adversarialSummary.opponentDefenses.map(defense => `<li>${defense}</li>`).join('')}
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="strategy-optimization">
            <h6><i class="fas fa-lightbulb"></i> 策略优化建议</h6>
            <div class="strategy-list">
                ${simulation.strategyOptimization.map((strategy, index) => `
                    <div class="strategy-item">
                        <div class="strategy-number">${index + 1}</div>
                        <div class="strategy-content">${strategy}</div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
    
    courtSimulation.innerHTML = html;
}

// 切换标签页
function switchTab(tabName) {
    // 更新标签按钮状态
    const tabBtns = document.querySelectorAll('.tab-btn');
    tabBtns.forEach(btn => btn.classList.remove('active'));
    event.target.closest('.tab-btn').classList.add('active');
    
    // 更新标签内容显示
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => content.classList.remove('active'));
    document.getElementById(tabName + '-tab').classList.add('active');
}

// 复制文档
function copyDocument() {
    const documentContent = document.getElementById('document-content');
    const text = documentContent.innerText;
    
    navigator.clipboard.writeText(text).then(() => {
        showNotification('文档已复制到剪贴板');
    }).catch(() => {
        showNotification('复制失败，请手动选择复制');
    });
}

// 下载文档
function downloadDocument() {
    const documentContent = document.getElementById('document-content');
    const text = documentContent.innerText;
    
    const blob = new Blob([text], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = '民事起诉状.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    showNotification('文档下载已开始');
}

// 显示通知
function showNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4f46e5;
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
        z-index: 1000;
        animation: slideInRight 0.3s ease-out;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease-out';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 添加滚动动画效果
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animation = 'fadeInUp 0.6s ease-out forwards';
            }
        });
    }, observerOptions);
    
    // 观察需要动画的元素
    const animatedElements = document.querySelectorAll('.input-form, .analysis-section, .overview-card');
    animatedElements.forEach(el => observer.observe(el));
});
