/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 系统介绍区样式 */
.hero-section {
    min-height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.animated-bg {
    position: absolute;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(1deg); }
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.brand-name {
    display: block;
    font-size: 4rem;
    background: linear-gradient(45deg, #fff, #e0e7ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.subtitle {
    display: block;
    font-size: 1.8rem;
    font-weight: 300;
    margin-top: 0.5rem;
}

.hero-tagline {
    font-size: 1.2rem;
    margin-bottom: 3rem;
    opacity: 0.9;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    animation: slideInUp 0.6s ease-out forwards;
    opacity: 0;
}

.feature-item:nth-child(1) { animation-delay: 0.1s; }
.feature-item:nth-child(2) { animation-delay: 0.2s; }
.feature-item:nth-child(3) { animation-delay: 0.3s; }
.feature-item:nth-child(4) { animation-delay: 0.4s; }

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.feature-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.feature-item i {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #e0e7ff;
}

.feature-item span {
    font-size: 1rem;
    font-weight: 500;
    text-align: center;
}

.cta-button {
    background: linear-gradient(45deg, #4f46e5, #7c3aed);
    color: white;
    border: none;
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 15px rgba(79, 70, 229, 0.4);
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(79, 70, 229, 0.6);
}

.cta-button i {
    transition: transform 0.3s ease;
}

.cta-button:hover i {
    transform: translateX(5px);
}

/* 智能交互区样式 */
.interactive-section {
    padding: 5rem 0;
    background: #f8fafc;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 3rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(45deg, #4f46e5, #7c3aed);
    border-radius: 2px;
}

.steps-indicator {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-bottom: 3rem;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    opacity: 0.5;
    transition: all 0.3s ease;
}

.step.active {
    opacity: 1;
}

.step-number {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.step.active .step-number {
    background: linear-gradient(45deg, #4f46e5, #7c3aed);
    color: white;
    transform: scale(1.1);
}

.step-label {
    font-weight: 500;
    color: #64748b;
}

.step.active .step-label {
    color: #1e293b;
    font-weight: 600;
}

.input-form {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.input-group {
    margin-bottom: 2rem;
    position: relative;
}

.input-group label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.input-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    font-size: 1rem;
    font-family: inherit;
    resize: vertical;
    transition: all 0.3s ease;
}

.input-group textarea:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.example-btn {
    position: absolute;
    top: 0;
    right: 0;
    background: #f1f5f9;
    border: 1px solid #e2e8f0;
    color: #64748b;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.example-btn:hover {
    background: #e2e8f0;
    color: #475569;
}

.analyze-btn {
    width: 100%;
    background: linear-gradient(45deg, #4f46e5, #7c3aed);
    color: white;
    border: none;
    padding: 1.2rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.analyze-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(79, 70, 229, 0.4);
}

.analyze-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.loading-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.loading-spinner i {
    font-size: 1.2rem;
}

/* 成果展示区样式 */
.results-section {
    padding: 5rem 0;
    background: white;
}

.results-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
}

.tab-btn {
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    color: #64748b;
    padding: 1rem 2rem;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.tab-btn.active {
    background: linear-gradient(45deg, #4f46e5, #7c3aed);
    color: white;
    border-color: transparent;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
}

.tab-content {
    display: none;
    animation: fadeIn 0.5s ease-in;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 文档容器样式 */
.document-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.document-header {
    background: linear-gradient(45deg, #4f46e5, #7c3aed);
    color: white;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.document-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
}

.document-actions {
    display: flex;
    gap: 1rem;
}

.action-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.3rem;
    font-size: 0.9rem;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.document-content {
    padding: 2rem;
    max-height: 600px;
    overflow-y: auto;
    font-size: 0.95rem;
    line-height: 1.8;
}

/* 分析报告样式 */
.analysis-container {
    max-width: 1000px;
    margin: 0 auto;
}

.report-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.overview-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.overview-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: linear-gradient(45deg, #4f46e5, #7c3aed);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.card-value {
    font-size: 2rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.2rem;
}

.card-label {
    color: #64748b;
    font-size: 0.9rem;
    font-weight: 500;
}

.analysis-section {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.analysis-section h4 {
    color: #1e293b;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.analysis-section h4 i {
    color: #4f46e5;
}

/* 页脚样式 */
.footer-section {
    background: #1e293b;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    margin-bottom: 2rem;
}

.value-summary h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #e2e8f0;
}

.value-summary p {
    color: #94a3b8;
    font-size: 1.1rem;
    line-height: 1.6;
}

.contact-info h4 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: #e2e8f0;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: #94a3b8;
}

.contact-item i {
    color: #4f46e5;
    width: 20px;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #334155;
    color: #64748b;
}

/* 文档内容样式 */
.document-section h4 {
    color: #1e293b;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 0.5rem;
}

.party-info p {
    margin-bottom: 1rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f1f5f9;
}

.case-cause {
    margin: 1.5rem 0;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
}

.claims-section, .facts-section {
    margin: 2rem 0;
}

.claims-section h5, .facts-section h5 {
    color: #374151;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.claims-section ol {
    padding-left: 1.5rem;
}

.claims-section li {
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

.fact-section {
    margin-bottom: 1.5rem;
}

.fact-section h6 {
    color: #4f46e5;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

/* 证据链时间线样式 */
.evidence-timeline {
    position: relative;
    padding: 1rem 0;
}

.evidence-timeline::before {
    content: '';
    position: absolute;
    left: 20px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #4f46e5, #7c3aed);
}

.timeline-item {
    position: relative;
    padding-left: 60px;
    margin-bottom: 2rem;
}

.timeline-marker {
    position: absolute;
    left: 12px;
    top: 0;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #4f46e5;
    border: 3px solid white;
    box-shadow: 0 0 0 3px #4f46e5;
}

.evidence-gap .timeline-marker {
    background: #ef4444;
    box-shadow: 0 0 0 3px #ef4444;
}

.timeline-content h6 {
    color: #1e293b;
    font-weight: 600;
    margin-bottom: 0.3rem;
}

.timeline-content p {
    color: #64748b;
    font-size: 0.9rem;
}

.gap-warning {
    color: #ef4444 !important;
    font-weight: 500;
}

/* 证据缺口警告样式 */
.gap-alert {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 1.5rem;
}

.alert-icon {
    font-size: 1.5rem;
    color: #ef4444;
}

.alert-content h6 {
    color: #dc2626;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.alert-content p {
    color: #7f1d1d;
    line-height: 1.6;
}

/* 法律洞察样式 */
.legal-statutes, .guiding-cases {
    margin-bottom: 2rem;
}

.legal-statutes h6, .guiding-cases h6 {
    color: #1e293b;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.statute-card, .case-card {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.statute-card:hover, .case-card:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
    transform: translateY(-2px);
}

.statute-card h7, .case-card h7 {
    color: #4f46e5;
    font-weight: 600;
    font-size: 1rem;
    display: block;
    margin-bottom: 0.5rem;
}

.case-number {
    color: #64748b;
    font-size: 0.9rem;
    font-style: italic;
    margin-bottom: 0.5rem;
}

/* 法庭推演样式 */
.arguments-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.our-arguments, .opponent-defenses {
    background: #f8fafc;
    border-radius: 10px;
    padding: 1.5rem;
}

.our-arguments {
    border-left: 4px solid #10b981;
}

.opponent-defenses {
    border-left: 4px solid #f59e0b;
}

.our-arguments h6, .opponent-defenses h6 {
    color: #1e293b;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.our-arguments h6 i {
    color: #10b981;
}

.opponent-defenses h6 i {
    color: #f59e0b;
}

.our-arguments ul, .opponent-defenses ul {
    list-style: none;
    padding: 0;
}

.our-arguments li, .opponent-defenses li {
    position: relative;
    padding-left: 1.5rem;
    margin-bottom: 0.8rem;
    line-height: 1.5;
    color: #374151;
}

.our-arguments li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #10b981;
    font-weight: bold;
}

.opponent-defenses li::before {
    content: '⚠';
    position: absolute;
    left: 0;
    color: #f59e0b;
}

/* 策略优化样式 */
.strategy-optimization h6 {
    color: #1e293b;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.strategy-optimization h6 i {
    color: #4f46e5;
}

.strategy-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.strategy-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 10px;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.strategy-item:hover {
    background: #e0f2fe;
    transform: translateX(5px);
}

.strategy-number {
    flex-shrink: 0;
    width: 30px;
    height: 30px;
    background: linear-gradient(45deg, #4f46e5, #7c3aed);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
}

.strategy-content {
    color: #1e293b;
    line-height: 1.6;
}

/* 通知动画 */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .brand-name {
        font-size: 3rem;
    }

    .subtitle {
        font-size: 1.4rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .steps-indicator {
        flex-direction: column;
        gap: 1rem;
    }

    .input-form {
        padding: 2rem 1.5rem;
    }

    .results-tabs {
        flex-direction: column;
        align-items: center;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .arguments-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .timeline-item {
        padding-left: 40px;
    }

    .evidence-timeline::before {
        left: 15px;
    }

    .timeline-marker {
        left: 7px;
    }
}
